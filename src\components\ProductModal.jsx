import React from 'react';
import Modal from './Modal';
import ProductForm from './ProductForm';

const ProductModal = ({ 
  isOpen, 
  onClose, 
  product = null, 
  categories = [], 
  onSubmit, 
  isLoading = false 
}) => {
  const isEditMode = !!product;
  const title = isEditMode ? 'Edit Product' : 'Add New Product';

  const handleSubmit = async (formData) => {
    try {
      await onSubmit(formData);
      onClose(); // Close modal on successful submission
    } catch (error) {
      // Error will be handled by the form component
      throw error;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="lg"
    >
      <ProductForm
        product={product}
        categories={categories}
        onSubmit={handleSubmit}
        onCancel={onClose}
        isLoading={isLoading}
      />
    </Modal>
  );
};

export default ProductModal;
