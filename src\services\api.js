import axios from 'axios';

// Base API configuration
const API_BASE_URL = 'https://fakestoreapi.com';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`Response received from ${response.config.url}:`, response.status);
    return response;
  },
  (error) => {
    console.error('Response error:', error);
    
    // Handle different error types
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. Please try again.');
    }
    
    if (!error.response) {
      throw new Error('Network error. Please check your connection.');
    }
    
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        throw new Error(data?.message || 'Bad request. Please check your input.');
      case 401:
        throw new Error('Unauthorized. Please check your credentials.');
      case 403:
        throw new Error('Forbidden. You do not have permission to perform this action.');
      case 404:
        throw new Error('Resource not found.');
      case 500:
        throw new Error('Server error. Please try again later.');
      default:
        throw new Error(data?.message || `Request failed with status ${status}`);
    }
  }
);

// Product API functions
export const productAPI = {
  // Get all products
  getAllProducts: async () => {
    try {
      const response = await api.get('/products');
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  },

  // Get single product by ID
  getProductById: async (id) => {
    try {
      const response = await api.get(`/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching product ${id}:`, error);
      throw error;
    }
  },

  // Get products by category
  getProductsByCategory: async (category) => {
    try {
      const response = await api.get(`/products/category/${category}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching products for category ${category}:`, error);
      throw error;
    }
  },

  // Get all categories
  getCategories: async () => {
    try {
      const response = await api.get('/products/categories');
      return response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  // Create new product
  createProduct: async (productData) => {
    try {
      const response = await api.post('/products', productData);
      console.log('Product created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  // Update existing product
  updateProduct: async (id, productData) => {
    try {
      const response = await api.put(`/products/${id}`, productData);
      console.log('Product updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error updating product ${id}:`, error);
      throw error;
    }
  },

  // Delete product
  deleteProduct: async (id) => {
    try {
      const response = await api.delete(`/products/${id}`);
      console.log('Product deleted successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Error deleting product ${id}:`, error);
      throw error;
    }
  },
};

// Utility functions for API operations
export const apiUtils = {
  // Validate product data before sending to API
  validateProductData: (productData) => {
    const errors = [];
    
    if (!productData.title || productData.title.trim().length === 0) {
      errors.push('Title is required');
    }
    
    if (!productData.price || productData.price <= 0) {
      errors.push('Price must be greater than 0');
    }
    
    if (!productData.category || productData.category.trim().length === 0) {
      errors.push('Category is required');
    }
    
    if (!productData.description || productData.description.trim().length === 0) {
      errors.push('Description is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Format product data for API
  formatProductData: (formData) => {
    return {
      title: formData.title?.trim(),
      price: parseFloat(formData.price),
      description: formData.description?.trim(),
      image: formData.image?.trim() || 'https://via.placeholder.com/300x300?text=No+Image',
      category: formData.category?.trim(),
    };
  },

  // Handle API errors consistently
  handleApiError: (error, defaultMessage = 'An error occurred') => {
    if (error.message) {
      return error.message;
    }
    return defaultMessage;
  },
};

export default api;
