<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="16" height="16" rx="3" fill="url(#gradient)" />
  
  <!-- Simplified Product Grid -->
  <g transform="translate(2, 2)">
    <rect x="0" y="0" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.9"/>
    <rect x="4.5" y="0" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.7"/>
    <rect x="9" y="0" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.9"/>
    
    <rect x="0" y="4.5" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.7"/>
    <rect x="4.5" y="4.5" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.9"/>
    <rect x="9" y="4.5" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.7"/>
    
    <rect x="0" y="9" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.9"/>
    <rect x="4.5" y="9" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.7"/>
    <rect x="9" y="9" width="3" height="3" rx="0.5" fill="white" fill-opacity="0.9"/>
    
    <!-- Central point -->
    <circle cx="6" cy="6" r="1" fill="white"/>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1D4ED8;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
