<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with subtle shadow -->
  <rect width="32" height="32" rx="8" fill="url(#gradient)" />
  <rect width="32" height="32" rx="8" fill="url(#overlay)" />

  <!-- Modern Product Management Icon -->
  <g transform="translate(6, 6)">
    <!-- Main Container -->
    <rect x="1" y="1" width="18" height="18" rx="2" fill="none" stroke="white" stroke-width="1.5" stroke-opacity="0.3"/>

    <!-- Product Grid Layout -->
    <rect x="3" y="3" width="4" height="4" rx="1" fill="white" fill-opacity="0.95"/>
    <rect x="8.5" y="3" width="4" height="4" rx="1" fill="white" fill-opacity="0.8"/>
    <rect x="14" y="3" width="4" height="4" rx="1" fill="white" fill-opacity="0.95"/>

    <rect x="3" y="8.5" width="4" height="4" rx="1" fill="white" fill-opacity="0.8"/>
    <rect x="8.5" y="8.5" width="4" height="4" rx="1" fill="white" fill-opacity="0.95"/>
    <rect x="14" y="8.5" width="4" height="4" rx="1" fill="white" fill-opacity="0.8"/>

    <rect x="3" y="14" width="4" height="4" rx="1" fill="white" fill-opacity="0.95"/>
    <rect x="8.5" y="14" width="4" height="4" rx="1" fill="white" fill-opacity="0.8"/>
    <rect x="14" y="14" width="4" height="4" rx="1" fill="white" fill-opacity="0.95"/>

    <!-- Management Symbol - Central Plus -->
    <g transform="translate(10, 10)">
      <circle r="2.5" fill="white"/>
      <path d="M-1.5 0 L1.5 0 M0 -1.5 L0 1.5" stroke="url(#gradient)" stroke-width="1.5" stroke-linecap="round"/>
    </g>
  </g>

  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#1D4ED8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="overlay" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:white;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:white;stop-opacity:0" />
    </radialGradient>
  </defs>
</svg>
