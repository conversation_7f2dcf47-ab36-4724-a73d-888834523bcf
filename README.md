# Product Management Dashboard

A comprehensive, modern React.js application for managing products with full CRUD functionality, built with Vite, Tailwind CSS, and the FakeStore API.

## Features

### Core Functionality
- **Complete CRUD Operations**: Create, Read, Update, and Delete products
- **Real-time Search**: Search products by title, description, or category
- **Advanced Filtering**: Filter by category and price range
- **Dual View Modes**: Toggle between grid/card layout and table layout
- **Sorting**: Sort products by title, price, category, or rating (ascending/descending)
- **Pagination**: Navigate through large product lists with customizable page sizes

### UI/UX Features
- **Responsive Design**: Mobile-first approach that works on all screen sizes
- **Modern Interface**: Clean, professional design with Tailwind CSS
- **Custom Branding**: Professional favicon and branding elements
- **Dark/Light Mode**: Fully functional theme toggle with smooth transitions
- **Loading States**: Visual feedback during API operations
- **Error Handling**: Comprehensive error messages and user feedback
- **Notifications**: Toast notifications for user actions
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support

### Technical Features
- **React 18+**: Built with modern React using functional components and hooks
- **Custom Hooks**: Reusable logic for products, notifications, and state management
- **API Integration**: Full integration with FakeStore API
- **Form Validation**: Client-side validation with user-friendly error messages
- **Modal System**: Reusable modal components for forms and confirmations
- **State Management**: Efficient local state management with React hooks

## Technology Stack

- **Frontend Framework**: React 18+ with Vite
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **API**: FakeStore API (https://fakestoreapi.com)
- **Build Tool**: Vite
- **Package Manager**: npm

## Installation & Setup

### Prerequisites
- Node.js (version 20.19+ or 22.12+ recommended)
- npm or yarn package manager

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd product-management-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to view the application

### Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

### Preview Production Build

```bash
npm run preview
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── Header.jsx       # Application header with navigation
│   ├── ProductCard.jsx  # Product card for grid view
│   ├── ProductTable.jsx # Product table for table view
│   ├── ProductForm.jsx  # Form for adding/editing products
│   ├── ProductModal.jsx # Modal wrapper for product form
│   ├── Modal.jsx        # Reusable modal component
│   ├── ConfirmDialog.jsx # Confirmation dialog for deletions
│   ├── LoadingSpinner.jsx # Loading indicator
│   ├── NotificationContainer.jsx # Toast notifications
│   ├── Pagination.jsx   # Pagination controls
│   └── SortDropdown.jsx # Sorting dropdown
├── hooks/               # Custom React hooks
│   ├── useProducts.js   # Product management logic
│   └── useNotification.js # Notification system
├── services/            # API and external services
│   └── api.js          # API functions and utilities
├── utils/              # Utility functions
├── App.jsx             # Main application component
├── main.jsx            # Application entry point
└── index.css           # Global styles and Tailwind imports
```

## API Integration

The application integrates with the FakeStore API for all product operations:

- **GET /products** - Fetch all products
- **GET /products/{id}** - Fetch single product
- **GET /products/categories** - Fetch all categories
- **GET /products/category/{category}** - Fetch products by category
- **POST /products** - Create new product
- **PUT /products/{id}** - Update existing product
- **DELETE /products/{id}** - Delete product

Note: The FakeStore API is a mock API, so POST, PUT, and DELETE operations will simulate success but won't persist data.

##  Responsive Design

The application is built with a mobile-first approach and includes:

- **Mobile (320px+)**: Single column layout, touch-friendly controls
- **Tablet (768px+)**: Two-column grid, optimized spacing
- **Desktop (1024px+)**: Multi-column grid, full feature set
- **Large screens (1280px+)**: Maximum width container, optimal viewing

##  Customization

### Styling
The application uses Tailwind CSS for styling. You can customize:

- **Colors**: Modify the color palette in `tailwind.config.js`
- **Typography**: Update font families and sizes
- **Spacing**: Adjust margins, padding, and layout spacing
- **Components**: Customize component styles in `src/index.css`

### Configuration
- **Pagination**: Adjust items per page in `useProducts.js`
- **API endpoints**: Modify base URL and endpoints in `services/api.js`
- **Validation rules**: Update validation logic in `services/api.js`

##  Testing

To test the application:

1. **Manual Testing**: Use the development server to test all features
2. **Cross-browser Testing**: Test in Chrome, Firefox, Safari, and Edge
3. **Responsive Testing**: Use browser dev tools to test different screen sizes
4. **API Testing**: Verify all CRUD operations work correctly

##  Deployment

The application can be deployed to any static hosting service:

1. **Build the application**: `npm run build`
2. **Deploy the `dist` folder** to your hosting service
3. **Configure routing** (if needed) for single-page application

Popular deployment options:
- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and commit: `git commit -m 'Add feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [FakeStore API](https://fakestoreapi.com) for providing the mock API
- [Tailwind CSS](https://tailwindcss.com) for the utility-first CSS framework
- [React](https://reactjs.org) for the component-based architecture
- [Vite](https://vitejs.dev) for the fast build tool
