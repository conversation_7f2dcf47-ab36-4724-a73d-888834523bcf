import React, { useState } from 'react';
import Header from './components/Header';
import ProductCard from './components/ProductCard';
import ProductTable from './components/ProductTable';
import LoadingSpinner from './components/LoadingSpinner';
import NotificationContainer from './components/NotificationContainer';
import ProductModal from './components/ProductModal';
import ConfirmDialog from './components/ConfirmDialog';
import Pagination from './components/Pagination';
import SortDropdown from './components/SortDropdown';
import { useProducts } from './hooks/useProducts';
import { useNotification } from './hooks/useNotification';

function App() {
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'table'
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Modal states
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [productToDelete, setProductToDelete] = useState(null);

  // Use custom hooks
  const {
    products,
    filteredProducts,
    categories,
    loading,
    error,
    searchTerm,
    selectedCategory,
    priceRange,
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    sortBy,
    sortOrder,
    setSearchTerm,
    setSelectedCategory,
    setPriceRange,
    clearFilters,
    addProduct,
    updateProduct,
    deleteProduct,
    handlePageChange,
    handleSortChange,
    setError,
  } = useProducts();

  const {
    notifications,
    removeNotification,
    showSuccess,
    showError,
    showInfo,
  } = useNotification();

  // Toggle view mode between grid and table
  const handleToggleView = () => {
    setViewMode(prev => prev === 'grid' ? 'table' : 'grid');
  };

  // Toggle theme
  const handleToggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  // Modal handlers
  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsProductModalOpen(true);
  };

  const handleEditProduct = (product) => {
    setSelectedProduct(product);
    setIsProductModalOpen(true);
  };

  const handleDeleteProduct = (product) => {
    setProductToDelete(product);
    setIsDeleteDialogOpen(true);
  };

  const handleViewProduct = (product) => {
    showInfo(`Viewing product: ${product.title}`);
  };

  // Product CRUD operations
  const handleProductSubmit = async (formData) => {
    try {
      if (selectedProduct) {
        // Edit mode
        await updateProduct(selectedProduct.id, formData);
        showSuccess(`Product "${formData.title}" updated successfully`);
      } else {
        // Add mode
        await addProduct(formData);
        showSuccess(`Product "${formData.title}" added successfully`);
      }
    } catch (err) {
      showError(`Failed to ${selectedProduct ? 'update' : 'add'} product: ${err.message}`);
      throw err; // Re-throw to prevent modal from closing
    }
  };

  const handleConfirmDelete = async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete.id);
      showSuccess(`Product "${productToDelete.title}" deleted successfully`);
      setProductToDelete(null);
    } catch (err) {
      showError(`Failed to delete product: ${err.message}`);
      throw err;
    }
  };

  // Close modals
  const closeProductModal = () => {
    setIsProductModalOpen(false);
    setSelectedProduct(null);
  };

  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setProductToDelete(null);
  };

  return (
    <div className={`min-h-screen transition-colors duration-200 ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <Header
        title="Product Management Dashboard"
        onToggleView={handleToggleView}
        viewMode={viewMode}
        onToggleTheme={handleToggleTheme}
        isDarkMode={isDarkMode}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Search */}
        <div className="mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors duration-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div>
                <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search Products
                </label>
                <input
                  type="text"
                  id="search"
                  className="input"
                  placeholder="Search by title, description, or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Category Filter */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category"
                  className="input"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category} value={category} className="capitalize">
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range
                </label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    className="input"
                    placeholder="Min"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                  />
                  <input
                    type="number"
                    className="input"
                    placeholder="Max"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                  />
                </div>
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <button
                  onClick={clearFilters}
                  className="btn btn-secondary w-full"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Count, Sort, and Add Button */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div className="text-sm text-gray-600">
            Showing {products.length} of {totalItems} products
            {(searchTerm || selectedCategory || priceRange.min || priceRange.max) && (
              <span className="ml-2 text-blue-600">(filtered from {filteredProducts.length})</span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <SortDropdown
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSortChange={handleSortChange}
            />
            <button
              onClick={handleAddProduct}
              className="btn btn-primary"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Add Product
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <button
                  onClick={() => setError(null)}
                  className="text-sm text-red-600 hover:text-red-500 mt-2 underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && <LoadingSpinner size="lg" text="Loading products..." />}

        {/* Products Display */}
        {!loading && (
          <>
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {products.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onView={handleViewProduct}
                    onEdit={handleEditProduct}
                    onDelete={handleDeleteProduct}
                  />
                ))}
              </div>
            ) : (
              <ProductTable
                products={products}
                onView={handleViewProduct}
                onEdit={handleEditProduct}
                onDelete={handleDeleteProduct}
                isLoading={loading}
              />
            )}

            {/* Empty State */}
            {products.length === 0 && !loading && (
              <div className="text-center py-12">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedCategory || priceRange.min || priceRange.max
                    ? 'Try adjusting your filters or search terms.'
                    : 'Get started by adding your first product.'}
                </p>
                {(searchTerm || selectedCategory || priceRange.min || priceRange.max) && (
                  <button
                    onClick={clearFilters}
                    className="mt-3 btn btn-primary"
                  >
                    Clear Filters
                  </button>
                )}
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
              />
            )}
          </>
        )}
      </main>

      {/* Notifications */}
      <NotificationContainer
        notifications={notifications}
        onRemove={removeNotification}
      />

      {/* Product Modal (Add/Edit) */}
      <ProductModal
        isOpen={isProductModalOpen}
        onClose={closeProductModal}
        product={selectedProduct}
        categories={categories}
        onSubmit={handleProductSubmit}
        isLoading={loading}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        onConfirm={handleConfirmDelete}
        title="Delete Product"
        message={
          productToDelete
            ? `Are you sure you want to delete "${productToDelete.title}"? This action cannot be undone.`
            : 'Are you sure you want to delete this product?'
        }
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        isLoading={loading}
      />
    </div>
  );
}

export default App;
