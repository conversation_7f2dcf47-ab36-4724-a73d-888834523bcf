import { useState, useEffect, useCallback } from 'react';
import { productAPI, apiUtils } from '../services/api';

export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [paginatedProducts, setPaginatedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });

  // Pagination and sorting
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [sortBy, setSortBy] = useState('title');
  const [sortOrder, setSortOrder] = useState('asc');

  // Fetch all products
  const fetchProducts = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await productAPI.getAllProducts();
      setProducts(data);
      setFilteredProducts(data);
    } catch (err) {
      setError(apiUtils.handleApiError(err, 'Failed to fetch products'));
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const data = await productAPI.getCategories();
      setCategories(data);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  }, []);

  // Add new product
  const addProduct = useCallback(async (productData) => {
    const validation = apiUtils.validateProductData(productData);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    setLoading(true);
    setError(null);
    try {
      const formattedData = apiUtils.formatProductData(productData);
      const newProduct = await productAPI.createProduct(formattedData);
      
      // Add to local state with a temporary ID for immediate UI update
      const productWithId = {
        ...newProduct,
        id: Date.now(), // Temporary ID since FakeStore API doesn't return real IDs
      };
      
      setProducts(prev => [productWithId, ...prev]);
      setFilteredProducts(prev => [productWithId, ...prev]);
      
      return productWithId;
    } catch (err) {
      setError(apiUtils.handleApiError(err, 'Failed to add product'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update product
  const updateProduct = useCallback(async (id, productData) => {
    const validation = apiUtils.validateProductData(productData);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    setLoading(true);
    setError(null);
    try {
      const formattedData = apiUtils.formatProductData(productData);
      const updatedProduct = await productAPI.updateProduct(id, formattedData);
      
      // Update local state
      const updateProductInState = (prevProducts) =>
        prevProducts.map(product =>
          product.id === id ? { ...updatedProduct, id } : product
        );
      
      setProducts(updateProductInState);
      setFilteredProducts(updateProductInState);
      
      return updatedProduct;
    } catch (err) {
      setError(apiUtils.handleApiError(err, 'Failed to update product'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete product
  const deleteProduct = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    try {
      await productAPI.deleteProduct(id);
      
      // Remove from local state
      const removeProductFromState = (prevProducts) =>
        prevProducts.filter(product => product.id !== id);
      
      setProducts(removeProductFromState);
      setFilteredProducts(removeProductFromState);
      
      return true;
    } catch (err) {
      setError(apiUtils.handleApiError(err, 'Failed to delete product'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Sort products
  const sortProducts = useCallback((productsToSort) => {
    return [...productsToSort].sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'category':
          aValue = a.category.toLowerCase();
          bValue = b.category.toLowerCase();
          break;
        case 'rating':
          aValue = a.rating?.rate || 0;
          bValue = b.rating?.rate || 0;
          break;
        case 'title':
        default:
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
      }

      if (sortOrder === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });
  }, [sortBy, sortOrder]);

  // Filter and sort products
  const filterAndSortProducts = useCallback(() => {
    let filtered = [...products];

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(term) ||
        product.description.toLowerCase().includes(term) ||
        product.category.toLowerCase().includes(term)
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    // Filter by price range
    if (priceRange.min !== '' || priceRange.max !== '') {
      filtered = filtered.filter(product => {
        const price = product.price;
        const min = priceRange.min === '' ? 0 : parseFloat(priceRange.min);
        const max = priceRange.max === '' ? Infinity : parseFloat(priceRange.max);
        return price >= min && price <= max;
      });
    }

    // Sort products
    const sorted = sortProducts(filtered);
    setFilteredProducts(sorted);

    // Reset to first page when filters change
    setCurrentPage(1);
  }, [products, searchTerm, selectedCategory, priceRange, sortProducts]);

  // Paginate products
  const paginateProducts = useCallback(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginated = filteredProducts.slice(startIndex, endIndex);
    setPaginatedProducts(paginated);
  }, [filteredProducts, currentPage, itemsPerPage]);

  // Apply filters and pagination when dependencies change
  useEffect(() => {
    filterAndSortProducts();
  }, [filterAndSortProducts]);

  useEffect(() => {
    paginateProducts();
  }, [paginateProducts]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchTerm('');
    setSelectedCategory('');
    setPriceRange({ min: '', max: '' });
    setCurrentPage(1);
  }, []);

  // Pagination helpers
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const totalItems = filteredProducts.length;

  // Sorting handlers
  const handleSortChange = useCallback((newSortBy, newSortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
  }, []);

  // Pagination handlers
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  }, []);

  // Initialize data on mount
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [fetchProducts, fetchCategories]);

  return {
    // Data
    products: paginatedProducts,
    allProducts: products,
    filteredProducts,
    categories,

    // State
    loading,
    error,
    searchTerm,
    selectedCategory,
    priceRange,

    // Pagination
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,

    // Sorting
    sortBy,
    sortOrder,

    // Actions
    fetchProducts,
    addProduct,
    updateProduct,
    deleteProduct,

    // Filters
    setSearchTerm,
    setSelectedCategory,
    setPriceRange,
    clearFilters,

    // Pagination
    handlePageChange,
    handleItemsPerPageChange,

    // Sorting
    handleSortChange,

    // Utils
    setError,
  };
};
